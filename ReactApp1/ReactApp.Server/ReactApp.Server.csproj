<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\reactapp1.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:5173</SpaProxyServerUrl>
    <RootNamespace>ReactApp1.Server</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.Authentication.ApiKey" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>8.*-*</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Graph" Version="5.83.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.4" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="3.9.4" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="8.12.1" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.12.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\reactapp.client\reactapp.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

</Project>
