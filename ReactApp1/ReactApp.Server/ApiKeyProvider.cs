using AspNetCore.Authentication.ApiKey;
using Microsoft.Extensions.Options;
using System.Security.Claims;

namespace frontend.Server
{
    public class ApiKeyProvider : IApiKeyProvider
    {
        private readonly Configuration.ApiKeyOptions _apiKeys;

        public ApiKeyProvider(IOptions<Configuration.ApiKeyOptions> apiKeys)
        {
            _apiKeys = apiKeys.Value;
        }

        public Task<IApiKey> ProvideAsync(string key)
        {
            var matchingKey = _apiKeys.ApiKeys.FirstOrDefault(settingsKey => 
                string.Equals(settingsKey.Key, key, StringComparison.Ordinal));

            if (matchingKey != null)
            {
                List<Claim> claims = [];
                IApiKey result = new ApiKey(key, "", claims);
                return Task.FromResult(result);
            }

            throw new KeyNotFoundException("API key not found.");
        }
    }
}
