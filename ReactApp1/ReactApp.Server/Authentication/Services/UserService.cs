using ReactApp.Server.Authentication.Interfaces;
using ReactApp.Server.Authentication.Models;
using UserInfo = ReactApp.Server.Authentication.Interfaces.UserInfo;

namespace ReactApp.Server.Authentication.Services
{
    public class UserService : IUserService
    {
        private readonly ITokenValidator _tokenValidator;

        public UserService(ITokenValidator tokenValidator)
        {
            _tokenValidator = tokenValidator;
        }

        public async Task<UserProfile> GetUserInfoAsync(string userId)
        {
            return await Task.FromResult(new UserProfile(
                userId,
                "<EMAIL>",
                "User Name",
                DateTime.UtcNow
            ));
        }

        public async Task<UserProfile> GetUserProfileAsync(string userId)
        {
            return await Task.FromResult(new UserProfile(
                userId,
                "<EMAIL>",
                "User Name",
                DateTime.UtcNow
            ));
        }

        public async Task<bool> IsUserAuthorizedAsync(string userId, string resource)
        {
            return await Task.FromResult(true);
        }

        Task<UserProfile> IUserService.GetUserInfoAsync(string userId)
        {
            return GetUserInfoAsync(userId);
        }

        public async Task<bool> IsUserAuthenticatedAsync(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            return await _tokenValidator.ValidateTokenAsync(token);
        }
    }
}