import {StrictMode} from 'react'
import {createRoot} from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import {MsalProvider} from "@azure/msal-react";
import {msalInstance, initializeMsal} from "./config/authConfig.ts";

// Initialize the application
const rootElement = document.getElementById('root')!;
const root = createRoot(rootElement);

// Initialize MSAL before rendering the app
initializeMsal().then(() => {
  root.render(
    <StrictMode>
      <MsalProvider instance={msalInstance}>
        <App />
      </MsalProvider>
    </StrictMode>
  );
}).catch(error => {
  console.error("Failed to initialize MSAL:", error);
});
