import { useMemo } from 'react';
import { useAuth } from '../auth/AuthContext';
import { ApiService } from '../services/ApiService';
import {apiConfig} from "../config/authConfig.ts";

export const useApiService = () => {
    const { getAccessToken } = useAuth();

    const apiService = useMemo(() => {
        return new ApiService(apiConfig.apiEndpoint, getAccessToken);
    }, [getAccessToken]);

    return apiService;
};