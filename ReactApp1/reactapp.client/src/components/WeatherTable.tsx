import React from 'react';
import {Forecast} from "../interfaces/IApiService.ts";

interface WeatherTableProps {
    forecasts: Forecast[];
}

const WeatherTable: React.FC<WeatherTableProps> = ({ forecasts }) => {
    if (forecasts.length === 0) {
        return <p>No weather data available.</p>;
    }

    return (
        <table className="weather-table">
            <thead>
            <tr>
                <th>Date</th>
                <th>Temp. (°C)</th>
                <th>Temp. (°F)</th>
                <th>Summary</th>
            </tr>
            </thead>
            <tbody>
            {forecasts.map((forecast) => (
                <tr key={forecast.date}>
                    <td>{new Date(forecast.date).toLocaleDateString()}</td>
                    <td>{forecast.temperatureC}°C</td>
                    <td>{forecast.temperatureF}°F</td>
                    <td>{forecast.summary}</td>
                </tr>
            ))}
            </tbody>
        </table>
    );
};

export default WeatherTable;