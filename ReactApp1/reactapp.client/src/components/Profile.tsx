import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface UserProfile {
    id: string;
    email: string;
    displayName: string;
    lastLogin: string;
}

const Profile: React.FC = () => {
    const { account, logout, apiService } = useAuth();
    const [profile, setProfile] = useState<UserProfile | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchProfile = async () => {
            try {
                setLoading(true);
                const data = await apiService.getUserProfile();
                setProfile(data);
                setError(null);
            } catch (err) {
                setError('Failed to load profile');
                console.error('Error fetching profile:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchProfile();
    }, [apiService]);

    if (loading) return <div>Loading profile...</div>;
    if (error) return <div>Error: {error}</div>;

    return (
        <div style={{ padding: '20px' }}>
            <h1>User Profile</h1>
            <div style={{ marginBottom: '20px' }}>
                <p><strong>Account:</strong> {account?.username}</p>
                {profile && (
                    <>
                        <p><strong>Display Name:</strong> {profile.displayName}</p>
                        <p><strong>Email:</strong> {profile.email}</p>
                        <p><strong>Last Login:</strong> {new Date(profile.lastLogin).toLocaleString()}</p>
                    </>
                )}
            </div>
            <button
                onClick={logout}
                style={{
                    padding: '10px 20px',
                    fontSize: '16px',
                    backgroundColor: '#d83b01',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                }}
            >
                Sign Out
            </button>
        </div>
    );
};

export default Profile;