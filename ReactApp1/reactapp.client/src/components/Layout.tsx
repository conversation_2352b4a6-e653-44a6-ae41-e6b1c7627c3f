import React from 'react';
import { Outlet, NavLink } from 'react-router-dom';
import { useAuth } from '../auth/AuthContext';
import UserInfo from './UserInfo';

const Layout: React.FC = () => {
    const { isAuthenticated, user, logout } = useAuth();

    return (
        <div className="app-layout">
            <header className="app-header">
                <div className="header-content">
                    <h1>Weather Forecast App</h1>
                    {isAuthenticated && (
                        <nav className="main-nav">
                            <NavLink
                                to="/weather"
                                className={({ isActive }) =>
                                    isActive ? 'nav-link active' : 'nav-link'
                                }
                            >
                                Weather
                            </NavLink>
                            <NavLink
                                to="/profile"
                                className={({ isActive }) =>
                                    isActive ? 'nav-link active' : 'nav-link'
                                }
                            >
                                Profile
                            </NavLink>
                        </nav>
                    )}
                </div>
                {isAuthenticated && (
                    <div className="header-user">
                        <UserInfo user={user} onLogout={logout} />
                    </div>
                )}
            </header>
            <main className="app-main">
                <Outlet />
            </main>
            <footer className="app-footer">
                <p>&copy; 2024 Weather Forecast App. All rights reserved.</p>
            </footer>
        </div>
    );
};

export default Layout;