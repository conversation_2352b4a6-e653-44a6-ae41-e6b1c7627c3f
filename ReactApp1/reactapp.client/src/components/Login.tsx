import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../auth/AuthContext';

const Login: React.FC = () => {
    const { isAuthenticated, login } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        if (isAuthenticated) {
            navigate('/weather', { replace: true });
        }
    }, [isAuthenticated, navigate]);

    return (
        <div className="login-page">
            <div className="login-card">
                <h2>Welcome to Weather Forecast App</h2>
                <p>Please sign in to access weather forecasts and your profile.</p>
                <button onClick={login} className="btn btn-primary btn-large">
                    <svg className="ms-logo" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
                        <rect x="1" y="1" width="9" height="9" fill="#f25022"/>
                        <rect x="1" y="11" width="9" height="9" fill="#00a4ef"/>
                        <rect x="11" y="1" width="9" height="9" fill="#7fba00"/>
                        <rect x="11" y="11" width="9" height="9" fill="#ffb900"/>
                    </svg>
                    Sign in with Microsoft
                </button>
            </div>
        </div>
    );
};

export default Login;